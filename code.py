import time
import threading
import json
from inputs import get_gamepad, get_key
import vgamepad as vg

RECORD_KEY = "KEY_J"
REPLAY_BUTTON = "ABS_HAT0Y"  # D-pad Down = 1

recording = []
is_recording = False
start_time = None

def record_inputs():
    global recording, is_recording, start_time
    print("Recording... press J again to stop.")
    start_time = time.time()
    while is_recording:
        events = get_gamepad()
        timestamp = time.time() - start_time
        for event in events:
            if event.ev_type in ['Key', 'Absolute']:
                recording.append((timestamp, event.ev_type, event.code, event.state))
        time.sleep(0.001)

def replay_inputs():
    print("Replaying...")
    gamepad = vg.VX360Gamepad()
    last_time = 0
    for timestamp, ev_type, code, state in recording:
        sleep_time = timestamp - last_time
        time.sleep(max(sleep_time, 0))
        last_time = timestamp

        if code == "ABS_RZ":  # RT
            gamepad.right_trigger(value=state)
        elif code == "ABS_Z":  # LT
            gamepad.left_trigger(value=state)
        elif code == "ABS_X":  # Left stick X
            gamepad.left_joystick(x_value=state, y_value=0)
        elif code == "ABS_Y":  # Left stick Y
            gamepad.left_joystick(x_value=0, y_value=state)
        elif code.startswith("BTN_"):
            btn = code.replace("BTN_", "XUSB_GAMEPAD_")
            if hasattr(vg.XUSB_BUTTON, btn):
                if state:
                    gamepad.press_button(getattr(vg.XUSB_BUTTON, btn))
                else:
                    gamepad.release_button(getattr(vg.XUSB_BUTTON, btn))
        gamepad.update()

def main_loop():
    global is_recording, recording
    print("Press 'J' on keyboard to start/stop recording. Press D-pad Down to replay.")
    dpad_down_held = False

    while True:
        # Handle keyboard for record
        key_events = get_key()
        for event in key_events:
            if event.ev_type == "Key" and event.code == RECORD_KEY and event.state == 1:
                if not is_recording:
                    is_recording = True
                    recording = []
                    threading.Thread(target=record_inputs).start()
                else:
                    is_recording = False
                    print("Recording stopped.")

        # Handle controller for replay
        gamepad_events = get_gamepad()
        for event in gamepad_events:
            if event.code == REPLAY_BUTTON:
                if event.state == 1 and not dpad_down_held and not is_recording:
                    dpad_down_held = True
                    replay_inputs()
                elif event.state == 0:
                    dpad_down_held = False

        time.sleep(0.005)

if __name__ == "__main__":
    main_loop()
